// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`agentSelectors > defaultAgentConfig > should merge DEFAULT_AGENT_CONFIG and defaultAgent(s).config correctly 1`] = `
{
  "chatConfig": {
    "autoCreateTopicThreshold": 2,
    "displayMode": "chat",
    "enableAutoCreateTopic": true,
    "enableCompressHistory": true,
    "enableHistoryCount": true,
    "enableReasoning": false,
    "historyCount": 20,
    "reasoningBudgetToken": 1024,
    "searchFCModel": {
      "model": "gpt-4.1-mini",
      "provider": "openai",
    },
    "searchMode": "off",
  },
  "model": "gpt-3.5-turbo",
  "openingQuestions": [],
  "params": {
    "frequency_penalty": 0,
    "presence_penalty": 0,
    "temperature": 0.7,
    "top_p": 1,
  },
  "plugins": [],
  "provider": "openai",
  "systemRole": "user",
  "tts": {
    "showAllLocaleVoice": false,
    "sttLocale": "auto",
    "ttsService": "openai",
    "voice": {
      "openai": "alloy",
    },
  },
}
`;
