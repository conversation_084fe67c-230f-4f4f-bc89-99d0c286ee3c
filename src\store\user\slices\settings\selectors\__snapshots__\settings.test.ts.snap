// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`settingsSelectors > currentSettings > should merge DEFAULT_SETTINGS and s.settings correctly 1`] = `
{
  "avatar": "avatar.jpg",
  "defaultAgent": {
    "config": {
      "model": "gpt-3.5-turbo",
      "params": {},
      "systemRole": "",
      "tts": {
        "showAllLocaleVoice": false,
        "sttLocale": "auto",
        "ttsService": "openai",
        "voice": {
          "openai": "alloy",
        },
      },
    },
    "meta": {
      "avatar": "Default Agent",
      "description": "Default agent for testing",
    },
  },
  "fontSize": 14,
  "language": "en-US",
  "languageModel": {
    "openAI": {
      "OPENAI_API_KEY": "openai-api-key",
      "endpoint": "https://openai-endpoint.com",
      "models": [
        "gpt-3.5-turbo",
      ],
    },
  },
  "neutralColor": "sand",
  "password": "password123",
  "primaryColor": "blue",
  "themeMode": "light",
  "tts": {
    "openAI": {
      "sttModel": "whisper-1",
      "ttsModel": "tts-1",
    },
    "sttAutoStop": true,
    "sttServer": "openai",
  },
}
`;

exports[`settingsSelectors > currentSystemAgent > should merge DEFAULT_SYSTEM_AGENT_CONFIG and s.settings.systemAgent correctly 1`] = `
{
  "agentMeta": {
    "model": "gpt-4.1-mini",
    "provider": "openai",
  },
  "enableAutoReply": true,
  "historyCompress": {
    "model": "gpt-4.1-mini",
    "provider": "openai",
  },
  "queryRewrite": {
    "enabled": true,
    "model": "gpt-4.1-mini",
    "provider": "openai",
  },
  "replyMessage": "Custom auto reply",
  "thread": {
    "model": "gpt-4.1-mini",
    "provider": "openai",
  },
  "topic": {
    "model": "gpt-4.1-mini",
    "provider": "openai",
  },
  "translation": {
    "model": "gpt-4.1-mini",
    "provider": "openai",
  },
}
`;

exports[`settingsSelectors > currentTTS > should merge DEFAULT_TTS_CONFIG and s.settings.tts correctly 1`] = `
{
  "openAI": {
    "sttModel": "whisper-2",
    "ttsModel": "tts-1",
  },
  "sttAutoStop": false,
  "sttServer": "openai",
}
`;

exports[`settingsSelectors > dalleConfig > should return the dalle configuration 1`] = `
{
  "apiKey": "dalle-api-key",
  "autoGenerate": true,
}
`;

exports[`settingsSelectors > defaultAgent > should merge DEFAULT_AGENT and s.settings.defaultAgent correctly 1`] = `
{
  "config": {
    "chatConfig": {
      "autoCreateTopicThreshold": 2,
      "displayMode": "chat",
      "enableAutoCreateTopic": true,
      "enableCompressHistory": true,
      "enableHistoryCount": true,
      "enableReasoning": false,
      "historyCount": 20,
      "reasoningBudgetToken": 1024,
      "searchFCModel": {
        "model": "gpt-4.1-mini",
        "provider": "openai",
      },
      "searchMode": "off",
    },
    "model": "gpt-3.5-turbo",
    "openingQuestions": [],
    "params": {
      "frequency_penalty": 0,
      "presence_penalty": 0,
      "temperature": 1,
      "top_p": 1,
    },
    "plugins": [],
    "provider": "openai",
    "systemRole": "user",
    "tts": {
      "showAllLocaleVoice": false,
      "sttLocale": "auto",
      "ttsService": "openai",
      "voice": {
        "openai": "alloy",
      },
    },
  },
  "meta": {
    "avatar": "agent-avatar.jpg",
    "description": "Test agent",
  },
}
`;

exports[`settingsSelectors > defaultAgentConfig > should merge DEFAULT_AGENT_CONFIG and defaultAgent(s).config correctly 1`] = `
{
  "chatConfig": {
    "autoCreateTopicThreshold": 2,
    "displayMode": "chat",
    "enableAutoCreateTopic": true,
    "enableCompressHistory": true,
    "enableHistoryCount": true,
    "enableReasoning": false,
    "historyCount": 20,
    "reasoningBudgetToken": 1024,
    "searchFCModel": {
      "model": "gpt-4.1-mini",
      "provider": "openai",
    },
    "searchMode": "off",
  },
  "model": "gpt-4",
  "openingQuestions": [],
  "params": {
    "frequency_penalty": 0,
    "presence_penalty": 0,
    "temperature": 0.7,
    "top_p": 1,
  },
  "plugins": [],
  "provider": "openai",
  "systemRole": "custom role",
  "tts": {
    "showAllLocaleVoice": false,
    "sttLocale": "auto",
    "ttsService": "openai",
    "voice": {
      "openai": "alloy",
    },
  },
}
`;

exports[`settingsSelectors > defaultAgentMeta > should merge DEFAULT_AGENT_META and defaultAgent(s).meta correctly 1`] = `
{
  "avatar": "agent-avatar.jpg",
  "description": "Test agent",
}
`;

exports[`settingsSelectors > getHotkeyById > should return default hotkey if not defined in settings 1`] = `undefined`;

exports[`settingsSelectors > getHotkeyById > should return the hotkey config for a given id 1`] = `
{
  "hotkey": "ctrl+shift+f",
  "scope": "global",
}
`;
