---
description: 
globs: src/database/models/**/*
alwaysApply: false
---
1. first read [lobe-chat-backend-architecture.mdc](mdc:.cursor/rules/lobe-chat-backend-architecture.mdc)
2. refer to the [_template.ts](mdc:src/database/models/_template.ts) to create new model
3. if an operation involves multiple models or complex queries, consider defining it in the `repositories` layer under `src/database/repositories/`