---
description: react flex layout package `react-layout-kit` usage
globs: 
alwaysApply: false
---
# React Layout Kit 使用指南

`react-layout-kit` 是一个功能丰富的 React flex 布局组件库，在 lobe-chat 项目中被广泛使用。以下是重点组件的使用方法：

## Flexbox 组件

Flexbox 是最常用的布局组件，用于创建弹性布局，类似于 CSS 的 `display: flex`。

### 基本用法

```jsx
import { Flexbox } from 'react-layout-kit';

// 默认垂直布局
<Flexbox>
  <div>子元素1</div>
  <div>子元素2</div>
</Flexbox>

// 水平布局
<Flexbox horizontal>
  <div>左侧元素</div>
  <div>右侧元素</div>
</Flexbox>
```

### 常用属性

- `horizontal`: 布尔值，设置为水平方向布局
- `flex`: 数值或字符串，控制 flex 属性
- `gap`: 数值，设置子元素之间的间距
- `align`: 对齐方式，如 'center', 'flex-start' 等
- `justify`: 主轴对齐方式，如 'space-between', 'center' 等
- `padding`: 内边距值
- `paddingInline`: 水平内边距值
- `paddingBlock`: 垂直内边距值
- `width/height`: 设置宽高，通常用 `'100%'` 或具体像素值
- `style`: 自定义样式对象

### 实际应用示例

```jsx
// 经典三栏布局
<Flexbox horizontal height={'100%'} width={'100%'}>
  {/* 左侧边栏 */}
  <Flexbox
    width={260}
    style={{
      borderRight: `1px solid ${theme.colorBorderSecondary}`,
      height: '100%',
      overflowY: 'auto',
    }}
  >
    <SidebarContent />
  </Flexbox>
  
  {/* 中间内容区 */}
  <Flexbox
    flex={1}
    style={{
      height: '100%',
    }}
  >
    {/* 主要内容 */}
    <Flexbox flex={1} padding={24} style={{ overflowY: 'auto' }}>
      <MainContent />
    </Flexbox>
    
    {/* 底部区域 */}
    <Flexbox
      style={{
        borderTop: `1px solid ${theme.colorBorderSecondary}`,
        padding: '16px 24px',
      }}
    >
      <Footer />
    </Flexbox>
  </Flexbox>
</Flexbox>
```

## Center 组件

Center 是对 Flexbox 的封装，使子元素水平和垂直居中。

### 基本用法

```jsx
import { Center } from 'react-layout-kit';

<Center width={'100%'} height={'100%'}>
  <Content />
</Center>
```

Center 组件继承了 Flexbox 的所有属性，同时默认设置了居中对齐。主要用于快速创建居中布局。

### 实际应用示例

```jsx
// 登录页面居中布局
<Flexbox height={'100%'} width={'100%'}>
  <Center height={'100%'} width={'100%'}>
    <LoginForm />
  </Center>
</Flexbox>

// 图标居中显示
<Center className={styles.icon} flex={'none'} height={40} width={40}>
  <Icon icon={icon} size={24} />
</Center>
```

## 最佳实践

1. 使用 `flex={1}` 让组件填充可用空间
2. 使用 `gap` 代替传统的 margin 设置元素间距
3. 嵌套 Flexbox 创建复杂布局
4. 设置 `overflow: 'auto'` 使内容可滚动
5. 使用 `horizontal` 创建水平布局，默认为垂直布局
6. 与 `antd-style` 的 `useTheme` hook 配合使用创建主题响应式的布局